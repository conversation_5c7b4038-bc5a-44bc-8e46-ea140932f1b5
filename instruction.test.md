# Capstone Project Evaluation Instructions

## Overview

You are an expert evaluator for the **AI-Powered Learning Management System (LMS)** capstone project. Your task is to assess student submissions across four sections: Frontend, Backend APIs, Databases, and AI-Powered Features.

> **Important:** This is a **prototype assessment** focusing on technical understanding, not production-ready implementation.

## Learning Outcomes

Students demonstrate ability to:

- Build responsive UIs using **HTML**, **CSS**, **Bootstrap**, **JavaScript**, and **React**
- Develop **RESTful APIs** using **Express.js** with proper validation
- Design and query **MySQL** and **MongoDB** databases
- Understand **Smart Search architecture** in AI-powered systems

## 🚨 Critical Rules - READ FIRST

**File Access:**

- ⚠️ **ONLY evaluate files within the `test/` directory** - Do NOT access solution files elsewhere
- 📁 Search subdirectories within `test/` if files are not found at root level

**Required Reading:**

- 📋 **MUST read corresponding `requirement.md` files** before evaluating each section
- 📊 **MUST review `grade.sample.md` and `rubric.md`** for scoring criteria before starting
- 🎯 **MUST read `output.png`, `output.md`, `output.pdf` or output files** (if available) to understand expected visual/functional requirements

**Output Requirements:**

- 🆕 **ALWAYS create NEW result file** - Never edit existing result files
- 📝 **File naming:** `result.{StudentName}.v{VersionNumber}.md` in main directory
- 🔢 **Version numbering:** v1 for first evaluation, v2 for corrections, v3 for re-evaluations, etc.
- ✅ **MUST verify total score** matches sum of individual task scores before submitting

## File Structure & Requirements

### Section 1: Frontend (30 points - 6 tasks × 5 points)

**Files to evaluate:**

- `Capstone_Section1_HTML_{Name}.html` - HTML/CSS/Bootstrap
- `Capstone_Section1_JS_{Name}.html` - JavaScript functionality
- `client/` - React components

**Required reading:** `html/requirement.md`, `javascript/requirement.md`, `react/requirement.md`
**Focus:** HTML/CSS layouts, Bootstrap cards, JavaScript validation, React components

### Section 2: Backend (10 points - 2 tasks × 5 points)

**Files to evaluate:**

- `lms-backend/server.js` - Express.js implementation
- `package.json` - Dependencies

**Required reading:** `express/requirement.md`
**Focus:** Express.js POST `/enroll` API, error handling

### Section 3: Databases (15 points - 3 tasks × 5 points)

**Files to evaluate:**

- `.sql` files - MySQL queries
- `Capstone_Section3_SQL_{Name}.md` - Documentation
- MongoDB implementation files

**Required reading:** `database/mongo/requirement.md`
**Focus:** MySQL table creation/queries, MongoDB implementation

### Section 4: AI Features (15 points - 3 tasks × 5 points)

**Files to evaluate:**

- `Capstone_Section4_{Name}.md` - Smart Search analysis

**Required reading:** `ai-features/requirement.md`
**Focus:** Smart Search architecture understanding

**📊 Total: 70 points (14 tasks)**

## Evaluation Process

### Step 1: Preparation

1. **Read Requirements:** Study all relevant `requirement.md` files for context
2. **Review Rubric:** Use `grade.sample.md` for scoring criteria
3. **Locate Files:** Find all student files in `test/` directory (search subdirectories if needed)

### Step 2: Technical Assessment

1. **Test Functionality:** Run code, verify features work as specified
2. **Check Implementation:** Evaluate code quality and requirements compliance
3. **Document Evidence:** Note specific observations with examples for feedback

### Step 3: Scoring & Documentation

1. **Score Each Task:** Use `grade.sample.md` criteria (Proficient/Developing/Below Expectation)
2. **Calculate Total:** Sum all individual task scores and verify accuracy
3. **Create Result File:** Generate new `result.{StudentName}.v{version}.md` file
4. **Provide Feedback:** Include specific evidence and actionable improvement recommendations

### Step 4: 🔍 Post-Evaluation Verification (Mandatory)

**CRITICAL:** After creating the result file, you MUST perform a comprehensive re-check to ensure evaluation accuracy.

1. **Re-read Grade Sample:** Open `grade.sample.md` and compare your scores against the rubric criteria
2. **Cross-Reference Evidence:** Verify that each score is supported by specific evidence in your evaluation
3. **Validate Score Calculations:** Manually recalculate all totals to ensure mathematical accuracy
4. **Check Completeness:** Ensure all 14 tasks have been evaluated with proper feedback
5. **Immediate Corrections:** If ANY discrepancies are found, create a new version (v2, v3, etc.) with corrections

## Output Template: result.{StudentName}.v{version}.md

Use this exact template for your evaluation report:

```markdown
# Capstone Project Evaluation Report

**Student:** [Name]
**Date:** [Date]
**Total Score:** [X]/70 points

## Section 1: Frontend (30 points)

### Task 1: CSS Layout Feature Boxes (5 points)

- **Score:** [X]/5
- **Level:** [Proficient/Developing/Below Expectation]
- **Feedback:** [Specific observations with examples]
- **Evidence:** [Code snippets, test results, or file references]

### Task 2: Bootstrap Cards (5 points)

- **Score:** [X]/5
- **Level:** [Proficient/Developing/Below Expectation]
- **Feedback:** [Specific observations with examples]
- **Evidence:** [Code snippets, test results, or file references]

[Continue for all remaining tasks...]

## Grading Summary

| Section     | Task                   | Score   | Max    |
| ----------- | ---------------------- | ------- | ------ |
| Frontend    | CSS Layout             | [X]     | 5      |
| Frontend    | Bootstrap Cards        | [X]     | 5      |
| Frontend    | JavaScript Validation  | [X]     | 5      |
| Frontend    | React Components       | [X]     | 5      |
| Frontend    | [Task 5]               | [X]     | 5      |
| Frontend    | [Task 6]               | [X]     | 5      |
| Backend     | Express.js API         | [X]     | 5      |
| Backend     | Error Handling         | [X]     | 5      |
| Database    | MySQL Queries          | [X]     | 5      |
| Database    | MongoDB Implementation | [X]     | 5      |
| Database    | [Task 3]               | [X]     | 5      |
| AI Features | Smart Search Analysis  | [X]     | 5      |
| AI Features | [Task 2]               | [X]     | 5      |
| AI Features | [Task 3]               | [X]     | 5      |
| **TOTAL**   |                        | **[X]** | **70** |

## Overall Assessment

**Strengths:** [List specific strengths with examples]
**Areas for Improvement:** [List specific areas with examples]
**Recommendations:** [Actionable steps for improvement]
**Files Evaluated:** [Complete list of files reviewed]
```

### Evidence Examples:

- **Code snippets:** `<div class="feature-box">` found in line 15
- **Test results:** "Form validation works correctly for empty email field"
- **File references:** "Missing React component in client/components/CourseCard.js"
- **Functionality:** "Bootstrap grid system implemented with proper responsive breakpoints"

## Quality Assurance Checklist

### ✅ Before Starting

- [ ] Read all relevant `requirement.md` files for context
- [ ] Review `grade.sample.md` for scoring criteria
- [ ] Confirm only evaluating files in `test/` directory

### ✅ During Evaluation

- [ ] Locate all required files (search subdirectories if needed)
- [ ] Test functionality and verify requirements compliance
- [ ] Document specific evidence with examples for each task
- [ ] Score all 14 tasks using rubric criteria

### ✅ Before Submitting

- [ ] Calculate total score manually and verify accuracy
- [ ] Cross-reference scores with `grade.sample.md` rubric criteria
- [ ] Confirm all 14 tasks have complete feedback with evidence
- [ ] Create NEW `result.{StudentName}.v{version}.md` file in main directory
- [ ] Include actionable improvement recommendations

## Troubleshooting Common Issues

**File Not Found:**

- Search subdirectories within `test/` folder
- Check for variations in naming (e.g., different capitalization)
- Document missing files in the evaluation report

**Version Numbering:**

- v1: First evaluation of a student
- v2: Corrections or updates to previous evaluation
- v3+: Additional revisions as needed

**Score Calculation Errors:**

- Always manually verify: Individual task scores must sum to total score
- If error found, create new version of result file with corrected scores

## Final Deliverable Requirements

Your evaluation is **COMPLETE** only when you have:

✅ **Created NEW result file:** `result.{StudentName}.v{version}.md` in main directory
✅ **Evaluated all 14 tasks:** Each with score, level, feedback, and evidence
✅ **Verified total score:** Manual calculation confirms accuracy (X/70 points)
✅ **Provided actionable feedback:** Specific examples and improvement recommendations
✅ **Listed all files reviewed:** Complete documentation of evaluation scope

**📝 File Naming Examples:** `result.Yan.v1.md`, `result.Alice.v2.md`, `result.John.v1.md`
